# 速信邮箱管理系统

一个基于Go和Vue3的企业级邮箱管理平台，提供邮箱管理、邮件收发、规则配置、验证码提取等功能。

## 功能特性

### 核心功能
- 🏢 **多域名管理** - 支持多个邮箱域名的统一管理
- 📧 **邮箱管理** - 批量创建、管理邮箱账户
- 📨 **邮件收发** - 支持SMTP/IMAP协议的邮件收发
- 🔍 **验证码提取** - 智能识别和提取邮件中的验证码
- 📋 **规则引擎** - 灵活的邮件处理规则配置
- 📊 **统计分析** - 详细的邮件统计和分析报告

### 技术特性
- 🚀 **高性能** - 基于Go语言，支持高并发处理
- 🔒 **安全可靠** - JWT认证、权限控制、数据加密
- 📱 **响应式设计** - 支持PC和移动端访问
- 🔧 **易于部署** - 支持Docker容器化部署
- 📈 **可扩展** - 模块化设计，易于扩展功能

## 技术栈

### 后端
- **语言**: Go 1.21+
- **框架**: Gin
- **数据库**: SQLite/MySQL
- **ORM**: GORM
- **认证**: JWT
- **日志**: Zap
- **配置**: Viper

### 前端
- **框架**: Vue 3
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **构建工具**: Vite
- **样式**: SCSS

## 项目结构

```
suxin_mail/
├── main.go                 # 程序入口
├── go.mod                  # Go模块文件
├── etc/                    # 配置文件
│   └── config.yaml
├── internal/               # 内部代码
│   ├── config/            # 配置管理
│   ├── constant/          # 常量定义
│   ├── handler/           # 请求处理器
│   ├── middleware/        # 中间件
│   ├── model/             # 数据模型
│   ├── result/            # 响应结构
│   ├── router/            # 路由配置
│   ├── svc/               # 服务上下文
│   └── types/             # 类型定义
├── pkg/                   # 公共包
│   ├── auth/              # 认证相关
│   ├── logger/            # 日志工具
│   └── utils/             # 工具函数
├── web/                   # 前端项目
│   ├── src/
│   │   ├── assets/        # 静态资源
│   │   ├── components/    # 组件
│   │   ├── router/        # 路由
│   │   ├── store/         # 状态管理
│   │   ├── utils/         # 工具函数
│   │   └── views/         # 页面
│   ├── package.json
│   └── vite.config.js
├── docs/                  # 文档
└── scripts/               # 脚本文件
```

## 快速开始

### 环境要求
- Go 1.21+
- Node.js 16+
- SQLite 3+ 或 MySQL 5.7+

### 安装部署

1. **克隆项目**
```bash
git clone https://github.com/your-org/suxin_mail.git
cd suxin_mail
```

2. **后端部署**
```bash
# 安装依赖
go mod tidy

# 复制配置文件
cp etc/config.yaml.example etc/config.yaml

# 编辑配置文件
vim etc/config.yaml

# 运行程序
go run main.go
```

3. **前端部署**
```bash
cd web

# 安装依赖
npm install

# 开发模式
npm run dev

# 生产构建
npm run build
```

### Docker部署

1. **构建镜像**
```bash
docker build -t suxin_mail .
```

2. **运行容器**
```bash
docker run -d \
  --name suxin_mail \
  -p 8081:8081 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/etc:/app/etc \
  suxin_mail
```

### 配置说明

主要配置项说明：

```yaml
# 应用配置
app:
  name: "邮件管理系统"
  version: "1.0.0"
  debug: true

# Web服务配置
web:
  port: 8081
  mode: "debug"

# 数据库配置
database:
  type: "sqlite"  # sqlite 或 mysql
  sqlite:
    path: "./data/email.db"

# JWT配置
jwt:
  secret: "your-secret-key"
  expire_hours: 24

# 邮件配置
email:
  default_smtp:
    host: "smtp.gmail.com"
    port: 587
    username: ""
    password: ""
```

## API文档

### 用户认证

#### 用户登录
```http
POST /api/user/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password"
}
```

#### 用户注册
```http
POST /api/user/register
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password"
}
```

### 邮箱管理

#### 获取邮箱列表
```http
GET /api/mailboxes?page=1&pageSize=10
Authorization: Bearer <token>
```

#### 创建邮箱
```http
POST /api/mailboxes
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password",
  "domain_id": 1
}
```

### 邮件管理

#### 获取邮件列表
```http
GET /api/emails?page=1&pageSize=10&mailbox_id=1
Authorization: Bearer <token>
```

#### 发送邮件
```http
POST /api/emails/send
Authorization: Bearer <token>
Content-Type: application/json

{
  "mailbox_id": 1,
  "to_emails": ["<EMAIL>"],
  "subject": "Test Email",
  "content": "Email content"
}
```

## 开发指南

### 添加新功能

1. **添加数据模型**
```go
// internal/model/new_model.go
type NewModel struct {
    ID        uint      `json:"id" gorm:"primarykey"`
    Name      string    `json:"name"`
    CreatedAt time.Time `json:"created_at"`
}
```

2. **添加处理器**
```go
// internal/handler/new_handler.go
func (h *NewHandler) Create(c *gin.Context) {
    // 处理逻辑
}
```

3. **添加路由**
```go
// internal/router/router.go
api.POST("/new", newHandler.Create)
```

### 代码规范

- 使用Go官方代码格式化工具：`go fmt`
- 遵循Go命名约定
- 添加必要的注释和文档
- 编写单元测试

### 测试

```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/model

# 生成测试覆盖率报告
go test -cover ./...
```

## 部署指南

### 生产环境部署

1. **环境准备**
- 确保服务器已安装Go 1.21+
- 配置MySQL数据库
- 配置反向代理（Nginx）

2. **配置文件**
```yaml
# 生产环境配置
app:
  debug: false

web:
  mode: "release"

database:
  type: "mysql"
  mysql:
    host: "localhost"
    port: 3306
    username: "email_user"
    password: "secure_password"
    database: "email_system"
```

3. **启动服务**
```bash
# 编译
go build -o suxin_mail main.go

# 后台运行
nohup ./suxin_mail -f etc/config.yaml > logs/app.log 2>&1 &
```

### 监控和维护

- 使用systemd管理服务
- 配置日志轮转
- 设置监控告警
- 定期备份数据

## 常见问题

### Q: 如何配置SMTP服务器？
A: 在配置文件中设置SMTP服务器信息，支持Gmail、Outlook等主流邮件服务商。

### Q: 如何添加新的邮件规则？
A: 通过管理后台的规则管理功能，可以添加验证码提取、转发、反垃圾等规则。

### Q: 数据库迁移失败怎么办？
A: 检查数据库连接配置，确保数据库用户有足够权限，查看日志获取详细错误信息。

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证，详见[LICENSE](LICENSE)文件。

## 联系我们

- 项目主页：https://github.com/your-org/suxin_mail
- 问题反馈：https://github.com/your-org/suxin_mail/issues
- 邮箱：<EMAIL>

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础邮箱管理功能
- 邮件收发功能
- 验证码提取功能
- 用户权限管理
