核心项目描述： 
"开发一个名为'速信邮箱管理系统'的企业级邮箱管理平台，采用前后端分离架构。系统需提供完整的邮件服务功能，包括邮件收发、用户管理、域名管理、反垃圾规则等核心模块，支持完整的SMTP/IMAP/POP3协议服务。管理员与用户权限隔离。"
技术栈要求：
1. 后端：Go语言 + GORM + SQLite，JWT刷新机制+OAuth2集成步骤
2. 前端：Vue.js + 响应式设计 + 深色模式/自定义主题
3. 安全：SSL/TLS加密 + bcrypt密码哈希 + SPF/DKIM/DMARC
4. 工具：Viper配置管理 + logrus日志 + 支持Docker容器化部署
5. 协议支持：完整实现SMTP/IMAP/POP3协议交互（）


规范后端接口只使用code msg  data

管理端功能:
- 用户管理：启用/禁用/批量操作，配额控制（域名数、邮箱数、附件大小等）
- 内容管控：邮箱/域名/转发规则的全局禁用（用户无法覆盖）
- 规则管理：验证码规则/反垃圾规则的公共化设置
- 系统配置：LOGO/标题/SMTP发信邮箱/注册开关
用户端功能:
1. 邮箱功能：
   - 批量生成邮箱（规则：长度/前缀/域名下拉选择）
   - 三方邮箱导入（微软/QQ/谷歌等，IMAP协议支持）
   - API收信（Token验证+多邮箱聚合查询）

2. 邮件功能：
   - 定时发送/群发/草稿保存
   - 签名模板+HTML编辑器

3. 账户安全：
   - 第三方登录（QQ/微软/谷歌，自动注册逻辑）
   - 密码找回通过SMTP验证码

UI/UX要求:
- 视觉：毛玻璃效果+卡片式布局+交互动画
- 主题：深色模式+用户自定义配色
- 适配：完美兼容移动端/桌面端

特殊逻辑强调:
1. 权限覆盖：管理员禁用的资源（邮箱/域名/规则）用户无法启用
2. 验证码规则：用户自定义规则需受管理员管控
3. 第三方登录：需与系统注册开关状态联动

管理端功能：
- 用户管理：启用/禁用/批量操作，配额控制（域名数、邮箱数、附件大小等）
- 内容管控：邮箱/域名/转发规则的全局禁用（用户无法覆盖）
- 规则管理：验证码规则/反垃圾规则的公共化设置
- 系统配置：LOGO/标题/SMTP发信邮箱/注册开关


用户端功能:
1. 邮箱功能：
   - 批量生成邮箱（规则：长度/前缀/域名下拉选择）
   - 三方邮箱导入（微软/QQ/谷歌等，IMAP协议支持）
   - API收信（Token验证+多邮箱聚合查询）

2. 邮件功能：
   - 定时发送/群发/草稿保存
   - 签名模板+HTML编辑器

3. 账户安全：
   - 第三方登录（QQ/微软/谷歌，自动注册逻辑）
   - 密码找回通过SMTP验证码

特殊逻辑强调 ：
1. 权限覆盖：管理员禁用的资源（邮箱/域名/规则）用户无法启用
2. 验证码规则：用户自定义规则需受管理员管控
3. 第三方登录：需与系统注册开关状态联动






数据库设计：
明确不使用原生sql,使用go SQLITE，符合以下需求的数据库方案：
1. 支持多租户隔离（企业用户vs个人用户）
2. 优化邮件存储结构（考虑正文/附件分离存储）
3. 需要以下表结构的详细DDL：
   - 用户表（含配额字段）
   - 邮箱账户表（关联域名）
   - 邮件元数据表（含SPAM评分）
   - 反垃圾规则表
   - 验证码规则表
特殊要求：
- 使用GORM模型，GO SQLite,不依赖CGO
- 说明索引设计策略


安全防护：
制定系统安全方案，需包含：
1. 传输层安全（TLS1.3配置最佳实践）
2. 认证安全（JWT刷新机制+OAuth2集成步骤）
3. 反垃圾体系（Rspamd集成方案）
4. 数据安全（邮件内容加密方案）


前端交互：
"设计现代化邮箱VUE界面：
1.深色模式：提供暗黑主题支持
2.效果：毛玻璃半透明，卡片式展示效果
3.自定义主题：允许用户自定义界面颜色
4.更多视觉效果：添加过渡动画和交互反馈
5. 响应式布局方案：（移动端适配策略）
6. 邮件列表页的关键优化点（虚拟滚动/懒加载）
7. 富文本编辑器集成（Quill）
8. 实时邮件推送方案（WebSocket）


扩展功能：
"实现以下高级功能：
1. 邮件智能分类
2. 跨域同步（ActiveSync协议集成）
3. 法律合规（邮件归档与审计）
4. 开放API（OAS3.0规范编写）






1.移除项目生成中，改变功能而遗留依赖
2.移除多余的文件,除了README.md，其余的.md文件及演示html 移动到docs文件夹
3.重新生成README.MD
4.移除虚拟测试数据

1.运行项目，进行测试


可以立即开始:
1.用户认证功能开发
2.邮件收发功能实现
3.数据库集成
4.安全功能加固
5.项目概述实现功能，前端页面与后端功能对应实现，检查前后端API接口正确，真实数据调用（非模拟数据)，如有无生成的页面则生成
6.检查所有前端页面中的按钮，标签跳转，数据调用，是否均已实现并正确无误！
7.完成以上动作后，对整个项目进行功能性检查


检查以下功能，前后端是否均已实现！
### 🎯 核心功能
- **📧 完整邮件系统**: 支持邮件收发、转发、附件管理
- **👥 多用户管理**: 用户注册、登录、权限控制
- **🏢 企业域名管理**: 自定义域名、邮箱分配
- **📊 实时数据统计**: 邮件统计、用户活跃度分析
- **🔒 安全认证**: JWT认证、密码加密、权限验证

### 👤 用户管理模块
- ✅ 用户注册/登录
- ✅ 个人信息管理
- ✅ 密码修改
- ✅ 权限控制

### 📧 邮件系统模块
- ✅ 邮件收发
- ✅ 邮箱管理
- ✅ 转发规则设置
- ✅ 附件上传下载

### 🏢 企业管理模块
- ✅ 域名管理
- ✅ 用户管理
- ✅ 邮箱分配
- ✅ 系统监控

### 📊 数据统计模块
- ✅ 邮件统计
- ✅ 用户活跃度
- ✅ 系统性能监控
- ✅ 实时数据展示



